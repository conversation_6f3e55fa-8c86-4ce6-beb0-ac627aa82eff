<template>
  <view class="private-chat-container">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="header-left" @click="goBack">
        <text class="back-icon">←</text>
        <text class="chat-title">{{ otherUserNickname }}</text>
      </view>
    </view>

    <!-- 消息列表 -->
    <scroll-view 
      class="message-list" 
      scroll-y 
      :scroll-top="scrollTop"
      @scrolltoupper="loadMoreMessages"
    >
      <view
        v-for="message in messages"
        :key="message.id"
        class="message-item"
        :class="{ 'own-message': message.sender_id.toString() === currentUser?.id?.toString() }"
        :style="{ border: message.sender_id.toString() === currentUser?.id?.toString() ? '2px solid red' : 'none' }"
      >
        <view class="message-wrapper">
          <!-- 其他人的头像（左边） -->
          <template v-if="message.sender_id.toString() !== currentUser?.id?.toString()">
            <view class="other-avatar">
              <image
                v-if="message.sender?.avatar_url"
                :src="message.sender.avatar_url"
                class="avatar-image"
              />
              <view v-else class="avatar-placeholder">
                <text>{{ (message.sender?.nickname || '用户').charAt(0) }}</text>
              </view>
            </view>
          </template>

          <view class="message-content">
            <view v-if="message.sender_id.toString() !== currentUser?.id?.toString()" class="sender-name">
              {{ message.sender?.nickname || '用户' }}
            </view>
            <view class="message-bubble" :class="{ 'own-bubble': message.sender_id.toString() === currentUser?.id?.toString() }">
              <text>{{ message.content }}</text>
            </view>
            <view class="message-time">
              {{ formatTime(message.created_at) }}
            </view>
          </view>

          <!-- 自己的头像（右边） -->
          <template v-if="message.sender_id.toString() === currentUser?.id?.toString()">
            <view class="own-avatar">
              <image
                v-if="currentUser?.avatar_url"
                :src="currentUser.avatar_url"
                class="avatar-image"
              />
              <view v-else class="avatar-placeholder">
                <text>{{ (currentUser?.nickname || '我').charAt(0) }}</text>
              </view>
            </view>
          </template>
        </view>
      </view>
      
      <!-- 加载更多提示 -->
      <view v-if="isLoadingMore" class="loading-more">
        <text>加载中...</text>
      </view>
      
      <!-- 空状态 -->
      <view v-if="!isLoading && messages.length === 0" class="empty-state">
        <text class="empty-text">开始你们的对话吧</text>
      </view>
    </scroll-view>

    <!-- 底部输入区域 -->
    <view class="input-section">
      <view class="input-container">
        <input 
          v-model="inputMessage"
          class="message-input" 
          placeholder="输入消息..."
          @confirm="sendMessage"
          confirm-type="send"
        />
        <button 
          class="send-btn" 
          @click="sendMessage" 
          :disabled="!inputMessage.trim() || isSending"
        >
          {{ isSending ? '发送中' : '发送' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, nextTick } from 'vue'
import { onLoad, onUnload } from '@dcloudio/uni-app'
import { chatAPI, utils } from '@/utils/api.js'

// 响应式数据
const messages = ref([])
const inputMessage = ref('')
const isLoading = ref(false)
const isLoadingMore = ref(false)
const isSending = ref(false)
const scrollTop = ref(0)
const hasMore = ref(true)

// 页面参数
const otherUserId = ref(null)
const otherUserNickname = ref('用户')

// 分页参数
const pagination = ref({
  page: 1,
  pageSize: 50
})

// 轮询相关
let pollingTimer = null
const pollingInterval = 3000 // 3秒轮询一次

// 计算属性
const currentUser = computed(() => {
  const user = utils.getCurrentUser()
  console.log('📱 Current user from storage:', user)
  console.log('📱 Current user ID type:', typeof user?.id, 'value:', user?.id)
  return user
})

// 生命周期
onLoad((options) => {
  const { userId, nickname } = options
  if (userId) {
    otherUserId.value = parseInt(userId)
    otherUserNickname.value = decodeURIComponent(nickname || '用户')
    loadMessages()
    startPolling()
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  }
})

onUnload(() => {
  stopPolling()
})

// 方法定义
const loadMessages = async (isRefresh = false) => {
  if (isLoading.value && !isRefresh) return
  
  try {
    if (isRefresh) {
      isLoadingMore.value = true
    } else {
      isLoading.value = true
    }
    
    const response = await chatAPI.getPrivateChatMessages(
      otherUserId.value,
      pagination.value.page,
      pagination.value.pageSize
    )
    
    if (response.success) {
      const newMessages = response.data?.data || []
      
      if (isRefresh) {
        // 加载更多（历史消息）
        messages.value = [...newMessages.reverse(), ...messages.value]
        pagination.value.page++
      } else {
        // 首次加载
        messages.value = newMessages.reverse()
        scrollToBottom()
      }
      
      hasMore.value = response.data?.pagination?.page < response.data?.pagination?.totalPages
    }
    
  } catch (error) {
    console.error('加载消息失败:', error)
    utils.handleError(error, '加载消息失败')
  } finally {
    isLoading.value = false
    isLoadingMore.value = false
  }
}

const loadMoreMessages = () => {
  if (!hasMore.value || isLoadingMore.value) return
  loadMessages(true)
}

const sendMessage = async () => {
  const content = inputMessage.value.trim()
  if (!content || isSending.value) return
  
  try {
    isSending.value = true
    
    const response = await chatAPI.sendPrivateMessage(otherUserId.value, content)
    
    if (response.success) {
      // 添加到消息列表
      messages.value.push(response.data)
      inputMessage.value = ''
      scrollToBottom()
    } else {
      uni.showToast({
        title: response.message || '发送失败',
        icon: 'none'
      })
    }
    
  } catch (error) {
    console.error('发送消息失败:', error)
    uni.showToast({
      title: '发送失败',
      icon: 'none'
    })
  } finally {
    isSending.value = false
  }
}



const formatTime = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now - date
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (date.toDateString() === now.toDateString()) { // 今天
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' })
  }
}

const scrollToBottom = () => {
  nextTick(() => {
    scrollTop.value = 999999
  })
}

const startPolling = () => {
  pollingTimer = setInterval(async () => {
    try {
      const lastMessage = messages.value[messages.value.length - 1]
      const lastMessageId = lastMessage ? lastMessage.id : 0
      
      const response = await chatAPI.getPrivateNewMessages(otherUserId.value, lastMessageId)
      
      if (response.success && response.data.length > 0) {
        messages.value.push(...response.data)
        scrollToBottom()
      }
    } catch (error) {
      console.error('轮询新消息失败:', error)
    }
  }, pollingInterval)
}

const stopPolling = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer)
    pollingTimer = null
  }
}

const goBack = () => {
  uni.navigateBack()
}
</script>

<style scoped>
.private-chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.header {
  background: white;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #e0e0e0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.back-icon {
  font-size: 36rpx;
  color: #007aff;
}

.chat-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.message-list {
  flex: 1;
  padding: 20rpx;
}

.message-item {
  margin-bottom: 30rpx;
}

.message-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.message-item.own-message .message-wrapper {
  flex-direction: row-reverse !important;
}

.other-avatar,
.own-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}

.message-content {
  max-width: 70%;
  min-width: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.own-message .message-content {
  align-items: flex-end;
}

.sender-name {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.message-bubble {
  background: white;
  padding: 20rpx;
  border-radius: 20rpx;
  word-wrap: break-word;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  display: inline-block;
  max-width: 100%;
  min-width: 80rpx;
}

.own-bubble {
  background: #007aff;
  color: white;
}

.message-time {
  font-size: 20rpx;
  color: #999;
  margin-top: 8rpx;
  text-align: left;
}

.own-message .message-time {
  text-align: right;
}

.loading-more {
  text-align: center;
  padding: 20rpx;
  color: #666;
  font-size: 24rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 20rpx;
}

.empty-text {
  color: #999;
  font-size: 28rpx;
}

.input-section {
  background: white;
  padding: 20rpx;
  border-top: 1rpx solid #e0e0e0;
}

.input-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.message-input {
  flex: 1;
  background: #f5f5f5;
  border: 1rpx solid #e0e0e0;
  border-radius: 25rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
}

.send-btn {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  min-width: 120rpx;
}

.send-btn:disabled {
  background: #ccc;
  color: #999;
}
</style>
